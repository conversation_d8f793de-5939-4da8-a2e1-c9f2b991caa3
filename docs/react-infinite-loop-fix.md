# React Infinite Loop Fix - Multi-Brand Bulk Toggle

## Overview
This document explains the fix for React error #185 "Maximum update depth exceeded" that occurred when toggling the Multi-Brand Bulk mode in the Parts Compatibility page.

## Problem Description
When users clicked the Multi-Brand Bulk toggle, the application would freeze and display React error #185, indicating an infinite loop in component re-rendering.

## Root Cause Analysis

### 1. Unstable Dependencies
The `brands` array was being recalculated on every render:
```typescript
// Problematic code
const brands = Array.from(
    new Map(availableModels.map(model => [model.brand.id, model.brand])).values()
);
```

### 2. Circular useEffect Dependencies
Multiple useEffect hooks were creating circular dependencies:
```typescript
// Problematic useEffect hooks
React.useEffect(() => {
    if (isBulkMode) {
        searchBulkBrands('');
    }
}, [selectedBrandTags, isBulkMode, searchBulkBrands]); // Circular dependency

React.useEffect(() => {
    if (isBulkMode && activeBrandId) {
        searchBulkModels('');
    }
}, [selectedModelsMap, activeBrandId, isBulkMode, searchBulkModels]); // Circular dependency
```

### 3. The Infinite Loop Chain
1. User toggles Multi-Brand Bulk mode
2. `isBulkMode` state changes
3. useEffect hooks trigger and call search functions
4. Search functions depend on `brands` array
5. `brands` array is recalculated, causing `searchBulkBrands` to be recreated
6. New function reference triggers useEffect hooks again
7. Loop continues infinitely

## Solution Implementation

### 1. Memoize Brands Calculation
```typescript
// Fixed code
const brands = useMemo(() => {
    return Array.from(
        new Map(availableModels.map(model => [model.brand.id, model.brand])).values()
    );
}, [availableModels]);
```

### 2. Remove Problematic useEffect Hooks
```typescript
// Removed these problematic hooks:
// React.useEffect(() => {
//     if (isBulkMode) {
//         searchBulkBrands('');
//     }
// }, [selectedBrandTags, isBulkMode, searchBulkBrands]);

// React.useEffect(() => {
//     if (isBulkMode && activeBrandId) {
//         searchBulkModels('');
//     }
// }, [selectedModelsMap, activeBrandId, isBulkMode, searchBulkModels]);

// Added comment explaining the optimization:
// Note: Removed problematic useEffect hooks that caused infinite loops
// The search functions are now only called when:
// 1. Bulk mode is first enabled (line 417-421)
// 2. Active brand changes (line 424-428)
// 3. User actually searches (via SearchableSelect onSearch prop)
```

### 3. Add Error Boundary Protection
Created `ErrorBoundary.tsx` component to catch and handle infinite loops gracefully:
```typescript
class ErrorBoundary extends Component<Props, State> {
  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Check if this is a React infinite loop error
    if (error.message.includes('Maximum update depth exceeded')) {
      console.error('Infinite loop detected! This is likely caused by circular dependencies in useEffect hooks.');
    }
  }
  // ... rest of implementation
}
```

## Best Practices to Prevent Similar Issues

### 1. Memoize Expensive Calculations
Always use `useMemo` for expensive calculations that don't need to run on every render:
```typescript
const expensiveValue = useMemo(() => {
    return someExpensiveCalculation(dependencies);
}, [dependencies]);
```

### 2. Careful useEffect Dependencies
Be mindful of useEffect dependencies, especially with functions:
```typescript
// ❌ Bad - function recreated on every render
const myFunction = () => { /* ... */ };
useEffect(() => {
    myFunction();
}, [myFunction]); // This will run on every render

// ✅ Good - stable function reference
const myFunction = useCallback(() => { /* ... */ }, [stableDependencies]);
useEffect(() => {
    myFunction();
}, [myFunction]); // This will only run when stableDependencies change
```

### 3. Use Error Boundaries
Wrap components that might have infinite loops in error boundaries:
```typescript
<ErrorBoundary>
    <ComponentThatMightHaveInfiniteLoops />
</ErrorBoundary>
```

### 4. Testing for Infinite Loops
Add tests that specifically check for infinite loop scenarios:
```typescript
it('does not cause infinite re-renders when toggling', async () => {
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => { });
    
    // Rapid toggling to test for infinite loops
    for (let i = 0; i < 10; i++) {
        fireEvent.click(toggle);
        await waitFor(() => { }, { timeout: 50 });
    }
    
    expect(consoleSpy).not.toHaveBeenCalledWith(
        expect.stringContaining('Maximum update depth exceeded')
    );
});
```

## Verification Steps
1. ✅ Multi-Brand Bulk toggle works without React errors
2. ✅ No infinite re-rendering loops
3. ✅ All search functionality works correctly
4. ✅ Component performance improved
5. ✅ Error boundary catches potential future issues
6. ✅ Comprehensive test coverage added

## Related Files
- `resources/js/pages/admin/Parts/Compatibility.tsx` - Main fix implementation
- `resources/js/components/ErrorBoundary.tsx` - Error boundary component
- `tests/components/MultiBrandBulkToggle.test.tsx` - Test coverage
- `docs/react-infinite-loop-fix.md` - This documentation

## Future Considerations
- Monitor for similar patterns in other components
- Consider adding ESLint rules to catch potential infinite loop patterns
- Regular code reviews focusing on React hooks dependencies
- Performance monitoring to catch similar issues early
