<?php

namespace Tests\Feature\Admin;

use App\Models\Brand;
use App\Models\Category;
use App\Models\MobileModel;
use App\Models\Part;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class BulkCompatibilityTest extends TestCase
{
    use RefreshDatabase;

    protected User $admin;
    protected Part $part;
    protected Brand $appleBrand;
    protected Brand $samsungBrand;
    protected Category $category;
    protected array $appleModels;
    protected array $samsungModels;

    protected function setUp(): void
    {
        parent::setUp();

        // Create admin user
        $this->admin = User::factory()->create(['role' => 'admin']);

        // Create test data
        $this->category = Category::factory()->create(['name' => 'Display']);
        
        $this->appleBrand = Brand::factory()->create(['name' => 'Apple']);
        $this->samsungBrand = Brand::factory()->create(['name' => 'Samsung']);
        
        $this->part = Part::factory()->create([
            'name' => 'Test Display',
            'category_id' => $this->category->id,
        ]);

        // Create Apple models
        $this->appleModels = [];
        for ($i = 1; $i <= 3; $i++) {
            $this->appleModels[] = MobileModel::factory()->create([
                'name' => "iPhone 1{$i}",
                'brand_id' => $this->appleBrand->id,
                'model_number' => "A200{$i}",
            ]);
        }

        // Create Samsung models
        $this->samsungModels = [];
        for ($i = 1; $i <= 3; $i++) {
            $this->samsungModels[] = MobileModel::factory()->create([
                'name' => "Galaxy S2{$i}",
                'brand_id' => $this->samsungBrand->id,
                'model_number' => "SM-S92{$i}",
            ]);
        }
    }

    public function test_admin_can_access_compatibility_page_with_bulk_mode()
    {
        $this->actingAs($this->admin);

        $response = $this->get("/admin/parts/{$this->part->id}/compatibility");

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('admin/Parts/Compatibility')
                ->has('part')
                ->has('availableModels')
                ->where('isAdminView', true)
        );
    }

    public function test_bulk_compatibility_endpoint_works_correctly()
    {
        $this->actingAs($this->admin);

        // Select models from both brands
        $selectedModelIds = [
            $this->appleModels[0]->id,
            $this->appleModels[1]->id,
            $this->samsungModels[0]->id,
        ];

        $response = $this->post("/admin/parts/{$this->part->id}/compatibility/bulk", [
            'model_ids' => $selectedModelIds,
            'compatibility_notes' => 'Bulk compatibility test',
            'is_verified' => true,
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Verify all models were added
        foreach ($selectedModelIds as $modelId) {
            $compatibility = $this->part->models()->where('model_id', $modelId)->first();
            $this->assertNotNull($compatibility);
            $this->assertEquals('Bulk compatibility test', $compatibility->pivot->compatibility_notes);
            $this->assertTrue((bool)$compatibility->pivot->is_verified);
        }
    }

    public function test_bulk_compatibility_prevents_duplicates()
    {
        $this->actingAs($this->admin);

        // First, add one model manually
        $this->part->models()->attach($this->appleModels[0]->id, [
            'compatibility_notes' => 'Manual compatibility',
            'is_verified' => false,
        ]);

        // Try to add the same model plus new ones via bulk
        $selectedModelIds = [
            $this->appleModels[0]->id, // Already exists
            $this->appleModels[1]->id, // New
            $this->samsungModels[0]->id, // New
        ];

        $response = $this->post("/admin/parts/{$this->part->id}/compatibility/bulk", [
            'model_ids' => $selectedModelIds,
            'compatibility_notes' => 'Bulk compatibility test',
            'is_verified' => true,
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Verify only new models were added
        $this->assertEquals(3, $this->part->models()->count());

        // Verify the existing model wasn't changed
        $existingCompatibility = $this->part->models()->where('model_id', $this->appleModels[0]->id)->first();
        $this->assertEquals('Manual compatibility', $existingCompatibility->pivot->compatibility_notes);
        $this->assertFalse((bool)$existingCompatibility->pivot->is_verified);

        // Verify new models were added correctly
        $newCompatibility1 = $this->part->models()->where('model_id', $this->appleModels[1]->id)->first();
        $this->assertEquals('Bulk compatibility test', $newCompatibility1->pivot->compatibility_notes);
        $this->assertTrue((bool)$newCompatibility1->pivot->is_verified);
    }

    public function test_bulk_compatibility_validation()
    {
        $this->actingAs($this->admin);

        // Test with empty model_ids array
        $response = $this->post("/admin/parts/{$this->part->id}/compatibility/bulk", [
            'model_ids' => [],
            'compatibility_notes' => 'Test',
            'is_verified' => false,
        ]);

        $response->assertSessionHasErrors(['model_ids']);

        // Test with invalid model IDs
        $response = $this->post("/admin/parts/{$this->part->id}/compatibility/bulk", [
            'model_ids' => [99999], // Non-existent model ID
            'compatibility_notes' => 'Test',
            'is_verified' => false,
        ]);

        $response->assertSessionHasErrors(['model_ids.0']);
    }

    public function test_bulk_compatibility_handles_all_existing_models()
    {
        $this->actingAs($this->admin);

        // First, add all models manually
        foreach ($this->appleModels as $model) {
            $this->part->models()->attach($model->id, [
                'compatibility_notes' => 'Existing',
                'is_verified' => false,
            ]);
        }

        // Try to add the same models via bulk
        $selectedModelIds = array_map(fn($model) => $model->id, $this->appleModels);

        $response = $this->post("/admin/parts/{$this->part->id}/compatibility/bulk", [
            'model_ids' => $selectedModelIds,
            'compatibility_notes' => 'Bulk test',
            'is_verified' => true,
        ]);

        $response->assertRedirect();
        $response->assertSessionHasErrors(['model_ids']);
    }

    public function test_bulk_compatibility_with_mixed_brands()
    {
        $this->actingAs($this->admin);

        // Select models from different brands
        $selectedModelIds = [
            $this->appleModels[0]->id,
            $this->appleModels[2]->id,
            $this->samsungModels[1]->id,
            $this->samsungModels[2]->id,
        ];

        $response = $this->post("/admin/parts/{$this->part->id}/compatibility/bulk", [
            'model_ids' => $selectedModelIds,
            'compatibility_notes' => 'Multi-brand compatibility',
            'is_verified' => false,
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Verify all models from both brands were added
        $this->assertEquals(4, $this->part->models()->count());

        // Verify models from both brands exist
        $appleCompatibilities = $this->part->models()
            ->whereHas('brand', fn($query) => $query->where('name', 'Apple'))
            ->count();
        $samsungCompatibilities = $this->part->models()
            ->whereHas('brand', fn($query) => $query->where('name', 'Samsung'))
            ->count();

        $this->assertEquals(2, $appleCompatibilities);
        $this->assertEquals(2, $samsungCompatibilities);
    }

    public function test_bulk_compatibility_success_message_includes_counts()
    {
        $this->actingAs($this->admin);

        // Add one model manually first
        $this->part->models()->attach($this->appleModels[0]->id, [
            'compatibility_notes' => 'Existing',
            'is_verified' => false,
        ]);

        // Try to add 3 models (1 existing, 2 new)
        $selectedModelIds = [
            $this->appleModels[0]->id, // Existing
            $this->appleModels[1]->id, // New
            $this->samsungModels[0]->id, // New
        ];

        $response = $this->post("/admin/parts/{$this->part->id}/compatibility/bulk", [
            'model_ids' => $selectedModelIds,
            'compatibility_notes' => 'Test',
            'is_verified' => false,
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Check that the success message mentions the counts
        $successMessage = session('success');
        $this->assertStringContainsString('Added compatibility for 2 models', $successMessage);
        $this->assertStringContainsString('1 models were already compatible', $successMessage);
    }
}
