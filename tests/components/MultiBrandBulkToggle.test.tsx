import Compatibility from '@/pages/admin/Parts/Compatibility';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';

// Mock Inertia
vi.mock('@inertiajs/react', () => ({
  useForm: () => ({
    data: {
      model_id: '',
      compatibility_notes: '',
      is_verified: false,
      display_type: '',
      display_size: '',
      location: '',
    },
    setData: vi.fn(),
    post: vi.fn(),
    processing: false,
    errors: {},
    reset: vi.fn(),
  }),
  router: {
    post: vi.fn(),
  },
}));

// Mock toast
vi.mock('react-hot-toast', () => ({
  default: {
    error: vi.fn(),
    success: vi.fn(),
  },
}));

// Mock delete confirmation hook
vi.mock('@/hooks/useDeleteConfirmation', () => ({
  useDeleteConfirmation: () => ({
    showDeleteConfirmation: vi.fn(),
  }),
}));

// Mock SearchableSelect component
vi.mock('@/components/ui/searchable-select', () => ({
  SearchableSelect: ({ onValueChange, onSearch, placeholder }: any) => (
    <div data-testid="searchable-select">
      <input
        placeholder={placeholder}
        onChange={(e) => onSearch && onSearch(e.target.value)}
        data-testid="search-input"
      />
      <button
        onClick={() => onValueChange && onValueChange('1')}
        data-testid="select-option"
      >
        Select Option
      </button>
    </div>
  ),
}));

// Mock UI components
vi.mock('@/components/ui/switch', () => ({
  Switch: ({ checked, onCheckedChange }: any) => (
    <input
      type="checkbox"
      checked={checked}
      onChange={(e) => onCheckedChange && onCheckedChange(e.target.checked)}
      data-testid="multi-brand-toggle"
    />
  ),
}));

vi.mock('@/components/ui/card', () => ({
  Card: ({ children }: any) => <div data-testid="card">{children}</div>,
  CardHeader: ({ children }: any) => <div data-testid="card-header">{children}</div>,
  CardTitle: ({ children }: any) => <h3 data-testid="card-title">{children}</h3>,
  CardDescription: ({ children }: any) => <p data-testid="card-description">{children}</p>,
  CardContent: ({ children }: any) => <div data-testid="card-content">{children}</div>,
}));

vi.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick, disabled }: any) => (
    <button onClick={onClick} disabled={disabled} data-testid="button">
      {children}
    </button>
  ),
}));

vi.mock('@/components/ui/label', () => ({
  Label: ({ children }: any) => <label data-testid="label">{children}</label>,
}));

vi.mock('@/components/ui/textarea', () => ({
  Textarea: ({ value, onChange }: any) => (
    <textarea
      value={value}
      onChange={onChange}
      data-testid="textarea"
    />
  ),
}));

vi.mock('@/components/ui/badge', () => ({
  Badge: ({ children }: any) => <span data-testid="badge">{children}</span>,
}));

vi.mock('lucide-react', () => ({
  Plus: () => <div data-testid="plus-icon" />,
  X: () => <div data-testid="x-icon" />,
}));

describe('MultiBrandBulkToggle', () => {
  const mockPart = {
    id: 1,
    name: 'Test Part',
    models: [],
  };

  const mockAvailableModels = [
    {
      id: 1,
      name: 'iPhone 13',
      model_number: 'A2482',
      brand: { id: 1, name: 'Apple', logo_url: null },
    },
    {
      id: 2,
      name: 'iPhone 14',
      model_number: 'A2649',
      brand: { id: 1, name: 'Apple', logo_url: null },
    },
    {
      id: 3,
      name: 'Galaxy S21',
      model_number: 'SM-G991',
      brand: { id: 2, name: 'Samsung', logo_url: null },
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the Multi-Brand Bulk toggle', () => {
    render(
      <Compatibility
        part={mockPart}
        availableModels={mockAvailableModels}
        showVerificationStatus={true}
      />
    );

    expect(screen.getByTestId('multi-brand-toggle')).toBeInTheDocument();
    expect(screen.getByText('Multi-Brand Bulk')).toBeInTheDocument();
  });

  it('toggles between single and bulk mode without errors', async () => {
    render(
      <Compatibility
        part={mockPart}
        availableModels={mockAvailableModels}
        showVerificationStatus={true}
      />
    );

    const toggle = screen.getByTestId('multi-brand-toggle');

    // Initially should be in single mode
    expect(toggle).not.toBeChecked();
    expect(screen.getByText('Add Compatibility')).toBeInTheDocument();

    // Toggle to bulk mode
    fireEvent.click(toggle);

    await waitFor(() => {
      expect(toggle).toBeChecked();
    });

    // Should show bulk mode UI
    expect(screen.getByText('Add Bulk Compatibility')).toBeInTheDocument();
    expect(screen.getByText('Add multiple model compatibilities across brands for this part')).toBeInTheDocument();

    // Toggle back to single mode
    fireEvent.click(toggle);

    await waitFor(() => {
      expect(toggle).not.toBeChecked();
    });

    expect(screen.getByText('Add Compatibility')).toBeInTheDocument();
  });

  it('does not cause infinite re-renders when toggling', async () => {
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => { });

    render(
      <Compatibility
        part={mockPart}
        availableModels={mockAvailableModels}
        showVerificationStatus={true}
      />
    );

    const toggle = screen.getByTestId('multi-brand-toggle');

    // Toggle multiple times rapidly
    for (let i = 0; i < 5; i++) {
      fireEvent.click(toggle);
      await waitFor(() => { }, { timeout: 100 });
    }

    // Should not have any console errors about infinite loops or React warnings
    expect(consoleSpy).not.toHaveBeenCalledWith(
      expect.stringContaining('Maximum update depth exceeded')
    );
    expect(consoleSpy).not.toHaveBeenCalledWith(
      expect.stringContaining('Cannot update a component')
    );

    consoleSpy.mockRestore();
  });

  it('shows brand selection in bulk mode', async () => {
    render(
      <Compatibility
        part={mockPart}
        availableModels={mockAvailableModels}
        showVerificationStatus={true}
      />
    );

    const toggle = screen.getByTestId('multi-brand-toggle');
    fireEvent.click(toggle);

    await waitFor(() => {
      expect(screen.getByText('Filter by Brand')).toBeInTheDocument();
    });

    // Should show searchable select for brands
    const searchableSelects = screen.getAllByTestId('searchable-select');
    expect(searchableSelects.length).toBeGreaterThan(0);
  });

  it('handles search functionality without errors', async () => {
    render(
      <Compatibility
        part={mockPart}
        availableModels={mockAvailableModels}
        showVerificationStatus={true}
      />
    );

    const toggle = screen.getByTestId('multi-brand-toggle');
    fireEvent.click(toggle);

    await waitFor(() => {
      expect(screen.getByText('Filter by Brand')).toBeInTheDocument();
    });

    // Test search functionality
    const searchInputs = screen.getAllByTestId('search-input');
    if (searchInputs.length > 0) {
      fireEvent.change(searchInputs[0], { target: { value: 'Apple' } });

      // Should not cause any errors
      await waitFor(() => { }, { timeout: 100 });
    }
  });

  it('maintains stable component state during mode changes', async () => {
    const { rerender } = render(
      <Compatibility
        part={mockPart}
        availableModels={mockAvailableModels}
        showVerificationStatus={true}
      />
    );

    const toggle = screen.getByTestId('multi-brand-toggle');

    // Toggle to bulk mode
    fireEvent.click(toggle);
    await waitFor(() => expect(toggle).toBeChecked());

    // Re-render component (simulating prop changes)
    rerender(
      <Compatibility
        part={mockPart}
        availableModels={mockAvailableModels}
        showVerificationStatus={true}
      />
    );

    // Should maintain bulk mode state
    expect(screen.getByTestId('multi-brand-toggle')).toBeChecked();
    expect(screen.getByText('Add Bulk Compatibility')).toBeInTheDocument();
  });
});
